/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  BodyHoldingsHoldingsDataView,
  HTTPValidationError,
  HoldingsDataViewResponse,
  HoldingsResponse,
  ReportingPeriod,
} from '../models/index';
import {
    BodyHoldingsHoldingsDataViewFromJSON,
    BodyHoldingsHoldingsDataViewToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    HoldingsDataViewResponseFromJSON,
    HoldingsDataViewResponseToJSON,
    HoldingsResponseFromJSON,
    HoldingsResponseToJSON,
    ReportingPeriodFromJSON,
    ReportingPeriodToJSON,
} from '../models/index';

export interface HoldingsHoldingsRequest {
    clientUuid: string;
    reportingPeriod: ReportingPeriod;
}

export interface HoldingsHoldingsDataViewRequest {
    query: string;
    bodyHoldingsHoldingsDataView: BodyHoldingsHoldingsDataView;
}

/**
 * 
 */
export class HoldingsApi extends runtime.BaseAPI {

    /**
     * Holdings
     */
    async holdingsHoldingsRaw(requestParameters: HoldingsHoldingsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<HoldingsResponse>> {
        if (requestParameters['clientUuid'] == null) {
            throw new runtime.RequiredError(
                'clientUuid',
                'Required parameter "clientUuid" was null or undefined when calling holdingsHoldings().'
            );
        }

        if (requestParameters['reportingPeriod'] == null) {
            throw new runtime.RequiredError(
                'reportingPeriod',
                'Required parameter "reportingPeriod" was null or undefined when calling holdingsHoldings().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['reportingPeriod'] != null) {
            queryParameters['reporting_period'] = requestParameters['reportingPeriod'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/holdings/{client_uuid}`.replace(`{${"client_uuid"}}`, encodeURIComponent(String(requestParameters['clientUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => HoldingsResponseFromJSON(jsonValue));
    }

    /**
     * Holdings
     */
    async holdingsHoldings(requestParameters: HoldingsHoldingsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<HoldingsResponse> {
        const response = await this.holdingsHoldingsRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get information about how holdings data should be grouped, pivoted, sorted, and filtered in the UI.
     * Holdings Data View
     */
    async holdingsHoldingsDataViewRaw(requestParameters: HoldingsHoldingsDataViewRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<HoldingsDataViewResponse>> {
        if (requestParameters['query'] == null) {
            throw new runtime.RequiredError(
                'query',
                'Required parameter "query" was null or undefined when calling holdingsHoldingsDataView().'
            );
        }

        if (requestParameters['bodyHoldingsHoldingsDataView'] == null) {
            throw new runtime.RequiredError(
                'bodyHoldingsHoldingsDataView',
                'Required parameter "bodyHoldingsHoldingsDataView" was null or undefined when calling holdingsHoldingsDataView().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['query'] != null) {
            queryParameters['query'] = requestParameters['query'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/holdings/holdings_data_view`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: BodyHoldingsHoldingsDataViewToJSON(requestParameters['bodyHoldingsHoldingsDataView']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => HoldingsDataViewResponseFromJSON(jsonValue));
    }

    /**
     * Get information about how holdings data should be grouped, pivoted, sorted, and filtered in the UI.
     * Holdings Data View
     */
    async holdingsHoldingsDataView(requestParameters: HoldingsHoldingsDataViewRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<HoldingsDataViewResponse> {
        const response = await this.holdingsHoldingsDataViewRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
