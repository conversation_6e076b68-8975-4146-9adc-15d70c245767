/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  CreateOrUpdateNoteResponse,
  EditNoteRequest,
  ExportNoteToPDFRequest,
  GenerateMeetingPrepRequest,
  HTTPValidationError,
  ListNotesResponse,
  MailtoResponse,
  NoteResponse,
  SearchAddSectionRequest,
  SearchAddSectionResponse,
  SwapAttendeesRequest,
} from '../models/index';
import {
    CreateOrUpdateNoteResponseFromJSON,
    CreateOrUpdateNoteResponseToJSON,
    EditNoteRequestFromJSON,
    EditNoteRequestToJSON,
    ExportNoteToPDFRequestFromJSON,
    ExportNoteToPDFRequestToJSON,
    GenerateMeetingPrepRequestFromJSON,
    GenerateMeetingPrepRequestToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    ListNotesResponseFromJSON,
    ListNotesResponseToJSON,
    MailtoResponseFromJSON,
    MailtoResponseToJSON,
    NoteResponseFromJSON,
    NoteResponseToJSON,
    SearchAddSectionRequestFromJSON,
    SearchAddSectionRequestToJSON,
    SearchAddSectionResponseFromJSON,
    SearchAddSectionResponseToJSON,
    SwapAttendeesRequestFromJSON,
    SwapAttendeesRequestToJSON,
} from '../models/index';

export interface NoteAddSectionToSummaryRequest {
    noteId: string;
    searchAddSectionRequest: SearchAddSectionRequest;
}

export interface NoteCreateOrUpdateNoteRequest {
    noteId?: string | null;
    meetingType?: string | null;
    noteType?: string | null;
    fileType?: string | null;
    meetingName?: string | null;
    attendees?: string | null;
    meetingLink?: string | null;
    meetingSourceId?: string | null;
    scheduledStartTime?: Date | null;
    scheduledEndTime?: Date | null;
    scheduledEventUuid?: string | null;
    clientName?: string | null;
    clientId?: string | null;
    audioData?: Blob | null;
}

export interface NoteDeleteNoteRequest {
    noteId: string;
}

export interface NoteEditNoteRequest {
    noteId: string;
    editNoteRequest: EditNoteRequest;
}

export interface NoteEmailFollowupWithMailtoContentRequest {
    noteId: string;
    template?: string;
}

export interface NoteEmailNoteRequest {
    noteId: string;
}

export interface NoteExportNoteRequest {
    noteId: string;
    exportNoteToPDFRequest?: ExportNoteToPDFRequest;
}

export interface NoteGenerateMeetingPrepRequest {
    noteId: string;
    generateMeetingPrepRequest?: GenerateMeetingPrepRequest;
}

export interface NoteGetNoteRequest {
    noteId: string;
}

export interface NoteListNotesRequest {
    notBefore?: number | null;
    notAfter?: number | null;
    q?: string | null;
    includeTeams?: Array<string> | null;
}

export interface NoteSwapAttendeeAliasesRequest {
    swapAttendeesRequest: SwapAttendeesRequest;
}

export interface NoteUpdateAuthorizedUsersRequest {
    noteId: string;
    requestBody: Array<string>;
}

export interface NoteUploadAudioChunkRequest {
    noteId: string;
    audioData: Blob;
    sequence: number;
    duration: number | null;
    mimeType: string | null;
    nonce: string | null;
}

/**
 * 
 */
export class NoteApi extends runtime.BaseAPI {

    /**
     * Add Section To Summary
     */
    async noteAddSectionToSummaryRaw(requestParameters: NoteAddSectionToSummaryRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SearchAddSectionResponse>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteAddSectionToSummary().'
            );
        }

        if (requestParameters['searchAddSectionRequest'] == null) {
            throw new runtime.RequiredError(
                'searchAddSectionRequest',
                'Required parameter "searchAddSectionRequest" was null or undefined when calling noteAddSectionToSummary().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}/add_section/`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SearchAddSectionRequestToJSON(requestParameters['searchAddSectionRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SearchAddSectionResponseFromJSON(jsonValue));
    }

    /**
     * Add Section To Summary
     */
    async noteAddSectionToSummary(requestParameters: NoteAddSectionToSummaryRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SearchAddSectionResponse> {
        const response = await this.noteAddSectionToSummaryRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Create or update a note with the provided data. This method is intended to be somewhat intelligent, and will handle new and existing notes, requests with mic audio data and requests associated with a meeting bot.
     * Create or update a note
     */
    async noteCreateOrUpdateNoteRaw(requestParameters: NoteCreateOrUpdateNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CreateOrUpdateNoteResponse>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const consumes: runtime.Consume[] = [
            { contentType: 'multipart/form-data' },
        ];
        // @ts-ignore: canConsumeForm may be unused
        const canConsumeForm = runtime.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): any };
        let useForm = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new URLSearchParams();
        }

        if (requestParameters['noteId'] != null) {
            formParams.append('note_id', requestParameters['noteId'] as any);
        }

        if (requestParameters['meetingType'] != null) {
            formParams.append('meeting_type', requestParameters['meetingType'] as any);
        }

        if (requestParameters['noteType'] != null) {
            formParams.append('note_type', requestParameters['noteType'] as any);
        }

        if (requestParameters['fileType'] != null) {
            formParams.append('file_type', requestParameters['fileType'] as any);
        }

        if (requestParameters['meetingName'] != null) {
            formParams.append('meeting_name', requestParameters['meetingName'] as any);
        }

        if (requestParameters['attendees'] != null) {
            formParams.append('attendees', requestParameters['attendees'] as any);
        }

        if (requestParameters['meetingLink'] != null) {
            formParams.append('meeting_link', requestParameters['meetingLink'] as any);
        }

        if (requestParameters['meetingSourceId'] != null) {
            formParams.append('meeting_source_id', requestParameters['meetingSourceId'] as any);
        }

        if (requestParameters['scheduledStartTime'] != null) {
            formParams.append('scheduled_start_time', (requestParameters['scheduledStartTime'] as any).toISOString());
        }

        if (requestParameters['scheduledEndTime'] != null) {
            formParams.append('scheduled_end_time', (requestParameters['scheduledEndTime'] as any).toISOString());
        }

        if (requestParameters['scheduledEventUuid'] != null) {
            formParams.append('scheduled_event_uuid', requestParameters['scheduledEventUuid'] as any);
        }

        if (requestParameters['clientName'] != null) {
            formParams.append('client_name', requestParameters['clientName'] as any);
        }

        if (requestParameters['clientId'] != null) {
            formParams.append('client_id', requestParameters['clientId'] as any);
        }

        if (requestParameters['audioData'] != null) {
            formParams.append('audio_data', requestParameters['audioData'] as any);
        }

        const response = await this.request({
            path: `/api/v2/note/create_or_update`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: formParams,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CreateOrUpdateNoteResponseFromJSON(jsonValue));
    }

    /**
     * Create or update a note with the provided data. This method is intended to be somewhat intelligent, and will handle new and existing notes, requests with mic audio data and requests associated with a meeting bot.
     * Create or update a note
     */
    async noteCreateOrUpdateNote(requestParameters: NoteCreateOrUpdateNoteRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CreateOrUpdateNoteResponse> {
        const response = await this.noteCreateOrUpdateNoteRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Delete a specific note by its ID. The user must be authorized to view the note.
     * Delete a specific note
     */
    async noteDeleteNoteRaw(requestParameters: NoteDeleteNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteDeleteNote().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Delete a specific note by its ID. The user must be authorized to view the note.
     * Delete a specific note
     */
    async noteDeleteNote(requestParameters: NoteDeleteNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.noteDeleteNoteRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Edit a specific note by its ID and changed note content. The user must be authorized to view the note.
     * Edit a specific note
     */
    async noteEditNoteRaw(requestParameters: NoteEditNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteEditNote().'
            );
        }

        if (requestParameters['editNoteRequest'] == null) {
            throw new runtime.RequiredError(
                'editNoteRequest',
                'Required parameter "editNoteRequest" was null or undefined when calling noteEditNote().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: EditNoteRequestToJSON(requestParameters['editNoteRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Edit a specific note by its ID and changed note content. The user must be authorized to view the note.
     * Edit a specific note
     */
    async noteEditNote(requestParameters: NoteEditNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.noteEditNoteRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Generate mailto link for a specific note by its ID. The user must be authorized to view the note.
     * Generate mailto link for a specific note
     */
    async noteEmailFollowupWithMailtoContentRaw(requestParameters: NoteEmailFollowupWithMailtoContentRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<MailtoResponse>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteEmailFollowupWithMailtoContent().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['template'] != null) {
            queryParameters['template'] = requestParameters['template'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}/email-followup-mailto`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => MailtoResponseFromJSON(jsonValue));
    }

    /**
     * Generate mailto link for a specific note by its ID. The user must be authorized to view the note.
     * Generate mailto link for a specific note
     */
    async noteEmailFollowupWithMailtoContent(requestParameters: NoteEmailFollowupWithMailtoContentRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<MailtoResponse> {
        const response = await this.noteEmailFollowupWithMailtoContentRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Email a specific note by its ID. The user must be authorized to view the note.
     * Email a specific note
     */
    async noteEmailNoteRaw(requestParameters: NoteEmailNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteEmailNote().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}/email`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Email a specific note by its ID. The user must be authorized to view the note.
     * Email a specific note
     */
    async noteEmailNote(requestParameters: NoteEmailNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.noteEmailNoteRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Export a note with selected sections.
     * Export Note
     */
    async noteExportNoteRaw(requestParameters: NoteExportNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteExportNote().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}/export`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: ExportNoteToPDFRequestToJSON(requestParameters['exportNoteToPDFRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Export a note with selected sections.
     * Export Note
     */
    async noteExportNote(requestParameters: NoteExportNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.noteExportNoteRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Generate meeting prep based on the current data we have access to, overwriting previous prep if it existed.
     * Generate meeting prep for a Note
     */
    async noteGenerateMeetingPrepRaw(requestParameters: NoteGenerateMeetingPrepRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteGenerateMeetingPrep().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}/generate_meeting_prep`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GenerateMeetingPrepRequestToJSON(requestParameters['generateMeetingPrepRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Generate meeting prep based on the current data we have access to, overwriting previous prep if it existed.
     * Generate meeting prep for a Note
     */
    async noteGenerateMeetingPrep(requestParameters: NoteGenerateMeetingPrepRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.noteGenerateMeetingPrepRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Retrieve details of a specific note by its ID. The user must be authorized to view the note.
     * Retrieve data of a specific note
     */
    async noteGetNoteRaw(requestParameters: NoteGetNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<NoteResponse>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteGetNote().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => NoteResponseFromJSON(jsonValue));
    }

    /**
     * Retrieve details of a specific note by its ID. The user must be authorized to view the note.
     * Retrieve data of a specific note
     */
    async noteGetNote(requestParameters: NoteGetNoteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<NoteResponse> {
        const response = await this.noteGetNoteRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List Notes
     */
    async noteListNotesRaw(requestParameters: NoteListNotesRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<ListNotesResponse>>> {
        const queryParameters: any = {};

        if (requestParameters['notBefore'] != null) {
            queryParameters['not_before'] = requestParameters['notBefore'];
        }

        if (requestParameters['notAfter'] != null) {
            queryParameters['not_after'] = requestParameters['notAfter'];
        }

        if (requestParameters['q'] != null) {
            queryParameters['q'] = requestParameters['q'];
        }

        if (requestParameters['includeTeams'] != null) {
            queryParameters['include_teams'] = requestParameters['includeTeams'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/list`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(ListNotesResponseFromJSON));
    }

    /**
     * List Notes
     */
    async noteListNotes(requestParameters: NoteListNotesRequest = {}, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<ListNotesResponse>> {
        const response = await this.noteListNotesRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Swap or assign speaker aliases between attendees in a meeting note
     * Swap attendee aliases in a note
     */
    async noteSwapAttendeeAliasesRaw(requestParameters: NoteSwapAttendeeAliasesRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['swapAttendeesRequest'] == null) {
            throw new runtime.RequiredError(
                'swapAttendeesRequest',
                'Required parameter "swapAttendeesRequest" was null or undefined when calling noteSwapAttendeeAliases().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/swap-attendee-aliases`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SwapAttendeesRequestToJSON(requestParameters['swapAttendeesRequest']),
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Swap or assign speaker aliases between attendees in a meeting note
     * Swap attendee aliases in a note
     */
    async noteSwapAttendeeAliases(requestParameters: NoteSwapAttendeeAliasesRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.noteSwapAttendeeAliasesRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Update Authorized Users
     */
    async noteUpdateAuthorizedUsersRaw(requestParameters: NoteUpdateAuthorizedUsersRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteUpdateAuthorizedUsers().'
            );
        }

        if (requestParameters['requestBody'] == null) {
            throw new runtime.RequiredError(
                'requestBody',
                'Required parameter "requestBody" was null or undefined when calling noteUpdateAuthorizedUsers().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/note/{note_id}/update-authorized-users`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: requestParameters['requestBody'],
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Update Authorized Users
     */
    async noteUpdateAuthorizedUsers(requestParameters: NoteUpdateAuthorizedUsersRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any | null | undefined > {
        const response = await this.noteUpdateAuthorizedUsersRaw(requestParameters, initOverrides);
        switch (response.raw.status) {
            case 200:
                return await response.value();
            case 204:
                return null;
            default:
                return await response.value();
        }
    }

    /**
     * Upload Audio Chunk
     */
    async noteUploadAudioChunkRaw(requestParameters: NoteUploadAudioChunkRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<any>> {
        if (requestParameters['noteId'] == null) {
            throw new runtime.RequiredError(
                'noteId',
                'Required parameter "noteId" was null or undefined when calling noteUploadAudioChunk().'
            );
        }

        if (requestParameters['audioData'] == null) {
            throw new runtime.RequiredError(
                'audioData',
                'Required parameter "audioData" was null or undefined when calling noteUploadAudioChunk().'
            );
        }

        if (requestParameters['sequence'] == null) {
            throw new runtime.RequiredError(
                'sequence',
                'Required parameter "sequence" was null or undefined when calling noteUploadAudioChunk().'
            );
        }

        if (requestParameters['duration'] == null) {
            throw new runtime.RequiredError(
                'duration',
                'Required parameter "duration" was null or undefined when calling noteUploadAudioChunk().'
            );
        }

        if (requestParameters['mimeType'] == null) {
            throw new runtime.RequiredError(
                'mimeType',
                'Required parameter "mimeType" was null or undefined when calling noteUploadAudioChunk().'
            );
        }

        if (requestParameters['nonce'] == null) {
            throw new runtime.RequiredError(
                'nonce',
                'Required parameter "nonce" was null or undefined when calling noteUploadAudioChunk().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const consumes: runtime.Consume[] = [
            { contentType: 'multipart/form-data' },
        ];
        // @ts-ignore: canConsumeForm may be unused
        const canConsumeForm = runtime.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): any };
        let useForm = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new URLSearchParams();
        }

        if (requestParameters['audioData'] != null) {
            formParams.append('audio_data', requestParameters['audioData'] as any);
        }

        if (requestParameters['sequence'] != null) {
            formParams.append('sequence', requestParameters['sequence'] as any);
        }

        if (requestParameters['duration'] != null) {
            formParams.append('duration', requestParameters['duration'] as any);
        }

        if (requestParameters['mimeType'] != null) {
            formParams.append('mime_type', requestParameters['mimeType'] as any);
        }

        if (requestParameters['nonce'] != null) {
            formParams.append('nonce', requestParameters['nonce'] as any);
        }

        const response = await this.request({
            path: `/api/v2/note/{note_id}/upload_audio_chunk`.replace(`{${"note_id"}}`, encodeURIComponent(String(requestParameters['noteId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: formParams,
        }, initOverrides);

        if (this.isJsonMime(response.headers.get('content-type'))) {
            return new runtime.JSONApiResponse<any>(response);
        } else {
            return new runtime.TextApiResponse(response) as any;
        }
    }

    /**
     * Upload Audio Chunk
     */
    async noteUploadAudioChunk(requestParameters: NoteUploadAudioChunkRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<any> {
        const response = await this.noteUploadAudioChunkRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
