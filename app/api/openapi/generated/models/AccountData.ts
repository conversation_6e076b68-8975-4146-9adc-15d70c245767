/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Data about an individual financial account.
 * @export
 * @interface AccountData
 */
export interface AccountData {
    /**
     * 
     * @type {string}
     * @memberof AccountData
     */
    number: string;
    /**
     * 
     * @type {string}
     * @memberof AccountData
     */
    name: string;
    /**
     * 
     * @type {number}
     * @memberof AccountData
     */
    totalValue?: number | null;
    /**
     * 
     * @type {number}
     * @memberof AccountData
     */
    totalHoldings?: number | null;
    /**
     * 
     * @type {number}
     * @memberof AccountData
     */
    percentageOfPortfolio?: number | null;
}

/**
 * Check if a given object implements the AccountData interface.
 */
export function instanceOfAccountData(value: object): value is AccountData {
    if (!('number' in value) || value['number'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function AccountDataFromJSON(json: any): AccountData {
    return AccountDataFromJSONTyped(json, false);
}

export function AccountDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): AccountData {
    if (json == null) {
        return json;
    }
    return {
        
        'number': json['number'],
        'name': json['name'],
        'totalValue': json['total_value'] == null ? undefined : json['total_value'],
        'totalHoldings': json['total_holdings'] == null ? undefined : json['total_holdings'],
        'percentageOfPortfolio': json['percentage_of_portfolio'] == null ? undefined : json['percentage_of_portfolio'],
    };
}

export function AccountDataToJSON(value?: AccountData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'number': value['number'],
        'name': value['name'],
        'total_value': value['totalValue'],
        'total_holdings': value['totalHoldings'],
        'percentage_of_portfolio': value['percentageOfPortfolio'],
    };
}

