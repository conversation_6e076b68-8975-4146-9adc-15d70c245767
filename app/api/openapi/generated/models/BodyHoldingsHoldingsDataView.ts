/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface BodyHoldingsHoldingsDataView
 */
export interface BodyHoldingsHoldingsDataView {
    /**
     * List of account names to use as potential filter options.
     * @type {Array<string>}
     * @memberof BodyHoldingsHoldingsDataView
     */
    accountNames: Array<string>;
    /**
     * List of ticker symbols to use as potential filter options.
     * @type {Array<string>}
     * @memberof BodyHoldingsHoldingsDataView
     */
    tickerSymbols: Array<string>;
    /**
     * List of account strategies to use as potential filter options.
     * @type {Array<string>}
     * @memberof BodyHoldingsHoldingsDataView
     */
    strategies: Array<string>;
    /**
     * List of account tax statuses to use as potential filter options.
     * @type {Array<string>}
     * @memberof BodyHoldingsHoldingsDataView
     */
    taxStatuses: Array<string>;
    /**
     * List of account time horizons to use as potential filter options.
     * @type {Array<string>}
     * @memberof BodyHoldingsHoldingsDataView
     */
    timeHorizons: Array<string>;
    /**
     * List of holding asset classes to use as potential filter options.
     * @type {Array<string>}
     * @memberof BodyHoldingsHoldingsDataView
     */
    assetClasses: Array<string>;
}

/**
 * Check if a given object implements the BodyHoldingsHoldingsDataView interface.
 */
export function instanceOfBodyHoldingsHoldingsDataView(value: object): value is BodyHoldingsHoldingsDataView {
    if (!('accountNames' in value) || value['accountNames'] === undefined) return false;
    if (!('tickerSymbols' in value) || value['tickerSymbols'] === undefined) return false;
    if (!('strategies' in value) || value['strategies'] === undefined) return false;
    if (!('taxStatuses' in value) || value['taxStatuses'] === undefined) return false;
    if (!('timeHorizons' in value) || value['timeHorizons'] === undefined) return false;
    if (!('assetClasses' in value) || value['assetClasses'] === undefined) return false;
    return true;
}

export function BodyHoldingsHoldingsDataViewFromJSON(json: any): BodyHoldingsHoldingsDataView {
    return BodyHoldingsHoldingsDataViewFromJSONTyped(json, false);
}

export function BodyHoldingsHoldingsDataViewFromJSONTyped(json: any, ignoreDiscriminator: boolean): BodyHoldingsHoldingsDataView {
    if (json == null) {
        return json;
    }
    return {
        
        'accountNames': json['account_names'],
        'tickerSymbols': json['ticker_symbols'],
        'strategies': json['strategies'],
        'taxStatuses': json['tax_statuses'],
        'timeHorizons': json['time_horizons'],
        'assetClasses': json['asset_classes'],
    };
}

export function BodyHoldingsHoldingsDataViewToJSON(value?: BodyHoldingsHoldingsDataView | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'account_names': value['accountNames'],
        'ticker_symbols': value['tickerSymbols'],
        'strategies': value['strategies'],
        'tax_statuses': value['taxStatuses'],
        'time_horizons': value['timeHorizons'],
        'asset_classes': value['assetClasses'],
    };
}

