/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ApiRoutersNoteModelsClient } from './ApiRoutersNoteModelsClient';
import {
    ApiRoutersNoteModelsClientFromJSON,
    ApiRoutersNoteModelsClientFromJSONTyped,
    ApiRoutersNoteModelsClientToJSON,
} from './ApiRoutersNoteModelsClient';
import type { FollowUp } from './FollowUp';
import {
    FollowUpFromJSON,
    FollowUpFromJSONTyped,
    FollowUpToJSON,
} from './FollowUp';

/**
 * 
 * @export
 * @interface ClientInteraction
 */
export interface ClientInteraction {
    /**
     * 
     * @type {string}
     * @memberof ClientInteraction
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof ClientInteraction
     */
    noteUuid: string | null;
    /**
     * 
     * @type {FollowUp}
     * @memberof ClientInteraction
     */
    clientPrep: FollowUp | null;
    /**
     * 
     * @type {FollowUp}
     * @memberof ClientInteraction
     */
    agenda: FollowUp | null;
    /**
     * 
     * @type {FollowUp}
     * @memberof ClientInteraction
     */
    advisorNotes: FollowUp | null;
    /**
     * 
     * @type {Array<ApiRoutersNoteModelsClient>}
     * @memberof ClientInteraction
     */
    clients: Array<ApiRoutersNoteModelsClient>;
}

/**
 * Check if a given object implements the ClientInteraction interface.
 */
export function instanceOfClientInteraction(value: object): value is ClientInteraction {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('noteUuid' in value) || value['noteUuid'] === undefined) return false;
    if (!('clientPrep' in value) || value['clientPrep'] === undefined) return false;
    if (!('agenda' in value) || value['agenda'] === undefined) return false;
    if (!('advisorNotes' in value) || value['advisorNotes'] === undefined) return false;
    if (!('clients' in value) || value['clients'] === undefined) return false;
    return true;
}

export function ClientInteractionFromJSON(json: any): ClientInteraction {
    return ClientInteractionFromJSONTyped(json, false);
}

export function ClientInteractionFromJSONTyped(json: any, ignoreDiscriminator: boolean): ClientInteraction {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'noteUuid': json['note_uuid'],
        'clientPrep': FollowUpFromJSON(json['client_prep']),
        'agenda': FollowUpFromJSON(json['agenda']),
        'advisorNotes': FollowUpFromJSON(json['advisor_notes']),
        'clients': ((json['clients'] as Array<any>).map(ApiRoutersNoteModelsClientFromJSON)),
    };
}

export function ClientInteractionToJSON(value?: ClientInteraction | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'note_uuid': value['noteUuid'],
        'client_prep': FollowUpToJSON(value['clientPrep']),
        'agenda': FollowUpToJSON(value['agenda']),
        'advisor_notes': FollowUpToJSON(value['advisorNotes']),
        'clients': ((value['clients'] as Array<any>).map(ApiRoutersNoteModelsClientToJSON)),
    };
}

