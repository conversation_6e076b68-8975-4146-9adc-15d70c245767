/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Denormalized data about a holding in a financial account.
 * 
 * This represents a single line item in an account holdings report (i.e., a holding of an asset in
 * an account). Fields that are prefixed with "account" represent data about the account, which
 * will be repeated for each holding in that account.
 * @export
 * @interface HoldingData
 */
export interface HoldingData {
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    accountName: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    accountId: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    accountStrategy: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    accountTaxStatus: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    accountTimeHorizon: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    accountHolder: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    tickerSymbol: string;
    /**
     * 
     * @type {string}
     * @memberof HoldingData
     */
    assetClass: string;
    /**
     * 
     * @type {Date}
     * @memberof HoldingData
     */
    asOfDate: Date;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    totalUnits: number;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    startValue: number;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    currentValue: number;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    percentageChange: number;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    change: number;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    percentageOfAccount?: number | null;
    /**
     * 
     * @type {number}
     * @memberof HoldingData
     */
    percentageOfPortfolio?: number | null;
}

/**
 * Check if a given object implements the HoldingData interface.
 */
export function instanceOfHoldingData(value: object): value is HoldingData {
    if (!('accountName' in value) || value['accountName'] === undefined) return false;
    if (!('accountId' in value) || value['accountId'] === undefined) return false;
    if (!('accountStrategy' in value) || value['accountStrategy'] === undefined) return false;
    if (!('accountTaxStatus' in value) || value['accountTaxStatus'] === undefined) return false;
    if (!('accountTimeHorizon' in value) || value['accountTimeHorizon'] === undefined) return false;
    if (!('accountHolder' in value) || value['accountHolder'] === undefined) return false;
    if (!('tickerSymbol' in value) || value['tickerSymbol'] === undefined) return false;
    if (!('assetClass' in value) || value['assetClass'] === undefined) return false;
    if (!('asOfDate' in value) || value['asOfDate'] === undefined) return false;
    if (!('totalUnits' in value) || value['totalUnits'] === undefined) return false;
    if (!('startValue' in value) || value['startValue'] === undefined) return false;
    if (!('currentValue' in value) || value['currentValue'] === undefined) return false;
    if (!('percentageChange' in value) || value['percentageChange'] === undefined) return false;
    if (!('change' in value) || value['change'] === undefined) return false;
    return true;
}

export function HoldingDataFromJSON(json: any): HoldingData {
    return HoldingDataFromJSONTyped(json, false);
}

export function HoldingDataFromJSONTyped(json: any, ignoreDiscriminator: boolean): HoldingData {
    if (json == null) {
        return json;
    }
    return {
        
        'accountName': json['account_name'],
        'accountId': json['account_id'],
        'accountStrategy': json['account_strategy'],
        'accountTaxStatus': json['account_tax_status'],
        'accountTimeHorizon': json['account_time_horizon'],
        'accountHolder': json['account_holder'],
        'tickerSymbol': json['ticker_symbol'],
        'assetClass': json['asset_class'],
        'asOfDate': (new Date(json['as_of_date'])),
        'totalUnits': json['total_units'],
        'startValue': json['start_value'],
        'currentValue': json['current_value'],
        'percentageChange': json['percentage_change'],
        'change': json['change'],
        'percentageOfAccount': json['percentage_of_account'] == null ? undefined : json['percentage_of_account'],
        'percentageOfPortfolio': json['percentage_of_portfolio'] == null ? undefined : json['percentage_of_portfolio'],
    };
}

export function HoldingDataToJSON(value?: HoldingData | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'account_name': value['accountName'],
        'account_id': value['accountId'],
        'account_strategy': value['accountStrategy'],
        'account_tax_status': value['accountTaxStatus'],
        'account_time_horizon': value['accountTimeHorizon'],
        'account_holder': value['accountHolder'],
        'ticker_symbol': value['tickerSymbol'],
        'asset_class': value['assetClass'],
        'as_of_date': ((value['asOfDate']).toISOString()),
        'total_units': value['totalUnits'],
        'start_value': value['startValue'],
        'current_value': value['currentValue'],
        'percentage_change': value['percentageChange'],
        'change': value['change'],
        'percentage_of_account': value['percentageOfAccount'],
        'percentage_of_portfolio': value['percentageOfPortfolio'],
    };
}

