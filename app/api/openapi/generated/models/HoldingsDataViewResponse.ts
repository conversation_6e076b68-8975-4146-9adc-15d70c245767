/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * Information about how holdings data should be grouped, pivoted, sorted, and filtered in the UI.
 * @export
 * @interface HoldingsDataViewResponse
 */
export interface HoldingsDataViewResponse {
    /**
     * List of column names that can be used to group data in the UI.
     * @type {Array<string>}
     * @memberof HoldingsDataViewResponse
     */
    groups: Array<string>;
    /**
     * List of column names that can be used to pivot data in the UI.
     * @type {Array<string>}
     * @memberof HoldingsDataViewResponse
     */
    pivots: Array<string>;
    /**
     * List of column names that can be used to sort data in the UI. Will be prefixed with + or - to indicate ascending or descending sort.
     * @type {Array<string>}
     * @memberof HoldingsDataViewResponse
     */
    sorts: Array<string>;
    /**
     * Dictionary where keys are column names and values are lists of filter values to select.
     * @type {{ [key: string]: Array<string>; }}
     * @memberof HoldingsDataViewResponse
     */
    filters: { [key: string]: Array<string>; };
}

/**
 * Check if a given object implements the HoldingsDataViewResponse interface.
 */
export function instanceOfHoldingsDataViewResponse(value: object): value is HoldingsDataViewResponse {
    if (!('groups' in value) || value['groups'] === undefined) return false;
    if (!('pivots' in value) || value['pivots'] === undefined) return false;
    if (!('sorts' in value) || value['sorts'] === undefined) return false;
    if (!('filters' in value) || value['filters'] === undefined) return false;
    return true;
}

export function HoldingsDataViewResponseFromJSON(json: any): HoldingsDataViewResponse {
    return HoldingsDataViewResponseFromJSONTyped(json, false);
}

export function HoldingsDataViewResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): HoldingsDataViewResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'groups': json['groups'],
        'pivots': json['pivots'],
        'sorts': json['sorts'],
        'filters': json['filters'],
    };
}

export function HoldingsDataViewResponseToJSON(value?: HoldingsDataViewResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'groups': value['groups'],
        'pivots': value['pivots'],
        'sorts': value['sorts'],
        'filters': value['filters'],
    };
}

