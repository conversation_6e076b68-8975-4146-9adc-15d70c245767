/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { HoldingData } from './HoldingData';
import {
    HoldingDataFromJSON,
    HoldingDataFromJSONTyped,
    HoldingDataToJSON,
} from './HoldingData';
import type { AccountData } from './AccountData';
import {
    AccountDataFromJSON,
    AccountDataFromJSONTyped,
    AccountDataToJSON,
} from './AccountData';

/**
 * Information about a client's financial accounts and holdings.
 * @export
 * @interface HoldingsResponse
 */
export interface HoldingsResponse {
    /**
     * 
     * @type {Array<AccountData>}
     * @memberof HoldingsResponse
     */
    accounts: Array<AccountData>;
    /**
     * 
     * @type {Array<HoldingData>}
     * @memberof HoldingsResponse
     */
    holdings: Array<HoldingData>;
}

/**
 * Check if a given object implements the HoldingsResponse interface.
 */
export function instanceOfHoldingsResponse(value: object): value is HoldingsResponse {
    if (!('accounts' in value) || value['accounts'] === undefined) return false;
    if (!('holdings' in value) || value['holdings'] === undefined) return false;
    return true;
}

export function HoldingsResponseFromJSON(json: any): HoldingsResponse {
    return HoldingsResponseFromJSONTyped(json, false);
}

export function HoldingsResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): HoldingsResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'accounts': ((json['accounts'] as Array<any>).map(AccountDataFromJSON)),
        'holdings': ((json['holdings'] as Array<any>).map(HoldingDataFromJSON)),
    };
}

export function HoldingsResponseToJSON(value?: HoldingsResponse | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'accounts': ((value['accounts'] as Array<any>).map(AccountDataToJSON)),
        'holdings': ((value['holdings'] as Array<any>).map(HoldingDataToJSON)),
    };
}

