/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by Z<PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The reporting period for which to fetch holdings data.
 * @export
 */
export const ReportingPeriod = {
    OneMonth: 'ONE_MONTH',
    ThisQuarter: 'THIS_QUARTER',
    ThreeMonths: 'THREE_MONTHS',
    SixMonths: 'SIX_MONTHS',
    YearToDate: 'YEAR_TO_DATE',
    OneYear: 'ONE_YEAR',
    ThreeYears: 'THREE_YEARS',
    FiveYears: 'FIVE_YEARS',
    TenYears: 'TEN_YEARS'
} as const;
export type ReportingPeriod = typeof ReportingPeriod[keyof typeof ReportingPeriod];


export function instanceOfReportingPeriod(value: any): boolean {
    for (const key in ReportingPeriod) {
        if (Object.prototype.hasOwnProperty.call(ReportingPeriod, key)) {
            if (ReportingPeriod[key as keyof typeof ReportingPeriod] === value) {
                return true;
            }
        }
    }
    return false;
}

export function ReportingPeriodFromJSON(json: any): ReportingPeriod {
    return ReportingPeriodFromJSONTyped(json, false);
}

export function ReportingPeriodFromJSONTyped(json: any, ignoreDiscriminator: boolean): ReportingPeriod {
    return json as ReportingPeriod;
}

export function ReportingPeriodToJSON(value?: ReportingPeriod | null): any {
    return value as any;
}

