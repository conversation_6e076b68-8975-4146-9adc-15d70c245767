import {
  ColDef,
  GetGroupRowAggParams,
  GridApi,
  GridState,
  IAggFuncParams,
  ICellRendererParams,
} from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import { themeQuartz } from "ag-grid-community";
import { ModuleRegistry } from "ag-grid-community";
import { AllEnterpriseModule } from "ag-grid-enterprise";
import { IntegratedChartsModule } from "ag-grid-enterprise";
import { AgChartsEnterpriseModule } from "ag-charts-enterprise";
import { useCallback, useEffect, useMemo, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { SquareArrowOutUpRight } from "lucide-react";
import { FetcherWithComponents } from "react-router";
import {
  HoldingData,
  HoldingsResponse,
  HoldingsDataViewResponse,
  ReportingPeriod,
} from "~/api/openapi/generated";
import { Button } from "~/@shadcn/ui/button";
import { MagicWandIcon } from "@radix-ui/react-icons";
import { Spinner } from "~/@ui/assets/Spinner";
import { Input } from "~/@shadcn/ui/input";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "~/@shadcn/ui/sheet";
import { Divider } from "~/@ui/Divider";
import { datadogLogs } from "@datadog/browser-logs";
import { useUserAgent } from "~/context/userAgent";

// Register the required feature modules for AG Grid.
ModuleRegistry.registerModules([
  AllEnterpriseModule,
  IntegratedChartsModule.with(AgChartsEnterpriseModule),
]);

//
// Formatters.
//

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  currencySign: "accounting",
});

const percentFormatter = new Intl.NumberFormat("en-US", {
  style: "percent",
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

const formatCurrencyValue = (value: number) => {
  return currencyFormatter.format(value);
};

const formatPercentValue = (value: number) => {
  return percentFormatter.format(value / 100);
};

//
// Basic column definitions.
//

// Shows a currency value, formatted, with green for positive and red for negative.
const currencyValueBaseColumnDef: ColDef<HoldingData> = {
  valueFormatter: (params) =>
    params.value ? formatCurrencyValue(params.value) : params.value,
  defaultAggFunc: "sum",
  cellStyle: (params) => {
    if (params.value >= 0) {
      return { color: "green", fontWeight: "bold" };
    } else {
      return { color: "red", fontWeight: "bold" };
    }
  },
  aggFunc: "sum",
};

// Shows a percentage value, formatted, with green for positive and red for negative.
const percentageChangeBaseColumnDef: ColDef<HoldingData> = {
  field: "percentageChange",
  headerName: "Percentage Change",
  enableValue: true,
  // Return a value and a weight for weighted average calculations.
  valueGetter: (params) => {
    const percentageChange = params.data?.percentageChange;
    const startValue = params.data?.startValue;

    return {
      value: percentageChange,
      weight: startValue,
    };
  },
  comparator: (valueA, valueB) => {
    const a = valueA?.value ?? 0;
    const b = valueB?.value ?? 0;
    return a - b;
  },
  valueFormatter: (params) => {
    let value: number | undefined = undefined;
    if (params.value instanceof Object && "value" in params.value) {
      value = params.value.value;
    } else if (params.value) {
      value = params.value;
    }
    return value ? formatPercentValue(value) : "";
  },
  defaultAggFunc: "weightedAvg",
  aggFunc: "weightedAvg",
  cellStyle: (params) => {
    if (params.value.value >= 0) {
      return { color: "green", fontWeight: "bold" };
    } else {
      return { color: "red", fontWeight: "bold" };
    }
  },
};

// An account name column.
const accountNameBaseColumDef: ColDef<HoldingData> = {
  field: "accountName",
  headerName: "Account Name",
};

// Shows a ticker symbol with a hyperlink to Yahoo Finance news for that symbol.
const tickerSymbolBaseColumDef: ColDef<HoldingData> = {
  field: "tickerSymbol",
  headerName: "Ticker Symbol",
  cellRenderer: (params: ICellRendererParams) => {
    if (!params.value) return null;
    const url = `https://finance.yahoo.com/quote/${params.value}/news`;
    return (
      <a
        href={url}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center gap-1"
      >
        {params.value}
        <SquareArrowOutUpRight className="h-4 w-4" />
      </a>
    );
  },
};

// A current-cash-value column.
const currentValueBaseColumnDef: ColDef<HoldingData> = {
  field: "currentValue",
  headerName: "Current value",
  enableValue: true,
  ...currencyValueBaseColumnDef,
};

// A full set of default column definitions.
//
// This is the base-state of the grid, before any user modifications.
const columnDefs: ColDef<HoldingData>[] = [
  {
    ...accountNameBaseColumDef,
    enableRowGroup: true,
    rowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "accountId",
    headerName: "Account ID",
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "accountStrategy",
    headerName: "Strategy",
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "accountTaxStatus",
    headerName: "Tax Status",
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "accountTimeHorizon",
    headerName: "Time Horizon",
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "accountHolder",
    headerName: "Account Holder",
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    ...tickerSymbolBaseColumDef,
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
  },
  {
    field: "assetClass",
    headerName: "Asset Class",
    enableRowGroup: true,
    enablePivot: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "asOfDate",
    headerName: "As Of Date",
    enableRowGroup: true,
    filter: true,
    sort: null,
    sortIndex: null,
    hide: true,
  },
  {
    field: "totalUnits",
    headerName: "Total Units",
    defaultAggFunc: "sum",
    enableValue: true,
    sort: null,
    sortIndex: null,
  },
  {
    field: "startValue",
    headerName: "Start Value",
    enableValue: true,
    ...currencyValueBaseColumnDef,
    sort: null,
    sortIndex: null,
  },
  {
    ...currentValueBaseColumnDef,
    sort: null,
    sortIndex: null,
  },
  {
    field: "change",
    headerName: "Change",
    enableValue: true,
    ...currencyValueBaseColumnDef,
    sort: null,
    sortIndex: null,
  },
  {
    ...percentageChangeBaseColumnDef,
    sort: null,
    sortIndex: null,
  },
];

// Given holdings data view information from the backend, returns a set of column definitions with
// the relevant pivots, groupings, sorts, and filters applied.
const columnsWithHoldingsDataView = (
  holdingsDataView: HoldingsDataViewResponse,
  api: GridApi | null
) => {
  const updatedColDefs = [...columnDefs];

  // Reset the columns to their initial states.
  api?.resetColumnState();

  // Sorts
  // TODO: sometimes the sorts don't get fully cleared in the UI.
  holdingsDataView.sorts.forEach((sort, i) => {
    const direction = sort.slice(0, 1) === "-" ? "desc" : "asc";
    const field = sort.slice(1);
    const col = updatedColDefs.find((c) => c.field === field);
    if (col) {
      col.sort = direction;
      col.sortIndex = i;
    }
  });

  // Filters
  const filterPromises: Promise<void>[] = [];
  // Apply the filters
  Object.entries(holdingsDataView.filters).forEach(([field, values]) => {
    const col = updatedColDefs.find((c) => c.field === field);
    if (col && api) {
      // Collect the filter change promises so we can wait on them before calling onFilterChanged()
      filterPromises.push(api.setColumnFilterModel(field, { values: values }));
    }
  });

  // Row groups and pivots.
  const finalizedColDefs = updatedColDefs.map((col) => {
    const updatedCol = { ...col };
    if (col.field && holdingsDataView.groups.includes(col.field)) {
      updatedCol.rowGroup = true;
    } else if (col.field && holdingsDataView.pivots.includes(col.field)) {
      updatedCol.aggFunc = updatedCol.defaultAggFunc;
    } else {
      updatedCol.rowGroup = false;
      updatedCol.aggFunc = undefined;
    }
    return updatedCol;
  });

  // If there are no pivots, update all of the aggregations functions to their defaults.
  if (holdingsDataView.pivots.length === 0) {
    finalizedColDefs.forEach((col) => {
      if (col.defaultAggFunc) {
        col.aggFunc = col.defaultAggFunc;
      }
    });
  }

  // Tell the grid that the filers have changed (as per its documentation).
  Promise.all(filterPromises).then(() => api?.onFilterChanged());

  return finalizedColDefs;
};

//
// Helpers for getting information from the row data to pass to the API that generates holdings data
// views.
//
// In order to support filtering, we need to know what the actual values are.
//

const accountsForRowData = (rowData: HoldingData[]) => {
  return Array.from(
    new Set(rowData.map((holding) => holding.accountName).filter(Boolean))
  );
};

const strategiesFromRowData = (rowData: HoldingData[]) => {
  return Array.from(
    new Set(rowData.map((holding) => holding.accountStrategy).filter(Boolean))
  );
};

const taxStatusesFromRowData = (rowData: HoldingData[]) => {
  return Array.from(
    new Set(rowData.map((holding) => holding.accountTaxStatus).filter(Boolean))
  );
};

const timeHorizonsFromRowData = (rowData: HoldingData[]) => {
  return Array.from(
    new Set(
      rowData.map((holding) => holding.accountTimeHorizon).filter(Boolean)
    )
  );
};

const tickerSymbolsFromRowData = (rowData: HoldingData[]) => {
  return Array.from(
    new Set(rowData.map((holding) => holding.tickerSymbol).filter(Boolean))
  );
};

const assetClassesFromRowData = (rowData: HoldingData[]) => {
  return Array.from(
    new Set(rowData.map((holding) => holding.assetClass).filter(Boolean))
  );
};

// Map the support reporting periods to user-friendly names.
const reportingPeriodOptions = {
  [ReportingPeriod.OneMonth]: "One month",
  [ReportingPeriod.ThisQuarter]: "This quarter",
  [ReportingPeriod.ThreeMonths]: "Three months",
  [ReportingPeriod.SixMonths]: "Six months",
  [ReportingPeriod.YearToDate]: "Year to Date",
  [ReportingPeriod.OneYear]: "One year",
  [ReportingPeriod.ThreeYears]: "Three years",
  [ReportingPeriod.FiveYears]: "Five years",
  [ReportingPeriod.TenYears]: "Ten years",
};

// A chart that displays holdings data by a given column, sorted per the comparator.
const HoldingsByColumnChart = ({
  data,
  columnDef,
  comparator,
}: {
  data: HoldingData[];
  columnDef: ColDef<HoldingData>;
  comparator: (a: HoldingData, b: HoldingData) => number;
}) => (
  <AgGridReact<HoldingData>
    theme={themeQuartz}
    rowData={[...data].sort(comparator)}
    defaultColDef={{
      sortable: false,
      suppressMovable: true,
      flex: 1,
    }}
    columnDefs={[accountNameBaseColumDef, tickerSymbolBaseColumDef, columnDef]}
  />
);

// The actual holdings tab component.
export const HoldingsTab = ({
  clientID,
  holdingsDataViewFetcher,
  holdingsDataFetcher,
}: {
  // The Zeplyn UUID of the client whose holdings are being displayed.
  clientID: string;

  // A fetcher that can be used to fetch holdings data views.
  holdingsDataViewFetcher: FetcherWithComponents<HoldingsDataViewResponse>;

  // A fetcher that can be used to fetch holdings data.
  holdingsDataFetcher: FetcherWithComponents<HoldingsResponse>;
}) => {
  const [reportingPeriod, setReportingPeriod] = useState<ReportingPeriod>(
    ReportingPeriod.YearToDate
  );
  const [gridAPI, setGridAPI] = useState<GridApi | null>(null);
  const [initialGridState, setInitialGridState] = useState<GridState | null>(
    null
  );
  const [colDefs, setColDefs] = useState(columnDefs);
  const [pivotMode, setPivotMode] = useState(false);
  const [rowData, setRowData] = useState<HoldingData[]>([]);
  const [savedGridStates, setSavedGridStates] = useState<{
    [name: string]: GridState;
  }>({});

  //
  // Saved grid states management.
  //
  const [newGridStateName, setNewGridStateName] = useState("");

  const storageKey = `holdings-grid-states`;

  // Load the grid states from storage.
  useEffect(() => {
    const savedStates = localStorage.getItem(storageKey);
    if (savedStates) {
      setSavedGridStates(JSON.parse(savedStates));
    }
  }, [storageKey]);

  const saveGridState = () => {
    if (!gridAPI || !newGridStateName) {
      return;
    }
    const currentState = gridAPI.getState();
    const newSavedStates = {
      ...savedGridStates,
      [newGridStateName]: currentState,
    };
    setSavedGridStates(newSavedStates);
    localStorage.setItem(storageKey, JSON.stringify(newSavedStates));
    setNewGridStateName("");
  };

  const overwriteGridState = (name: string) => {
    if (!gridAPI) {
      return;
    }
    const currentState = gridAPI.getState();
    const newSavedStates = {
      ...savedGridStates,
      [name]: currentState,
    };
    setSavedGridStates(newSavedStates);
    localStorage.setItem(storageKey, JSON.stringify(newSavedStates));
  };

  const deleteGridState = (name: string) => {
    const newSavedStates = { ...savedGridStates };
    delete newSavedStates[name];
    setSavedGridStates(newSavedStates);
    localStorage.setItem(storageKey, JSON.stringify(newSavedStates));
  };

  const restoreGridState = (name: string) => {
    if (!gridAPI || !savedGridStates[name]) {
      datadogLogs.logger.error("Tried to restore unknown grid state", { name });
      return;
    }
    gridAPI.setState(savedGridStates[name]);
  };

  // Update the columns when the holdings data view fetch is done
  useEffect(() => {
    if (holdingsDataViewFetcher.state !== "idle") {
      return;
    }
    if (!holdingsDataViewFetcher.data) {
      return;
    }
    setColDefs(
      columnsWithHoldingsDataView(holdingsDataViewFetcher.data, gridAPI)
    );
  }, [gridAPI, holdingsDataViewFetcher.data, holdingsDataViewFetcher.state]);

  // Fetches holdings data from the backend.
  const fetchHoldings = useCallback(() => {
    const formData = new FormData();
    formData.append("actionType", "get-holdings-data");
    formData.append("id", clientID);
    formData.append("reportingPeriod", reportingPeriod);
    holdingsDataFetcher.submit(formData, { method: "post" });
  }, [clientID, reportingPeriod, holdingsDataFetcher]);

  // Fetch holdings data on initial render
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(fetchHoldings, []);

  // Fetch holdings data when reporting period changes
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(fetchHoldings, [reportingPeriod]);

  // Show a spinner while the query is loading.
  useEffect(() => {
    if (holdingsDataFetcher.state !== "idle") {
      return;
    }
    if (!holdingsDataFetcher.data) {
      return;
    }
    setRowData(holdingsDataFetcher.data.holdings);
  }, [holdingsDataFetcher.data, holdingsDataFetcher.state]);

  // Create a custom weighted average aggregation function.
  const aggFuncs = useMemo(() => {
    return {
      weightedAvg(
        params: IAggFuncParams<
          HoldingData,
          { value: number | null; weight: number }
        >
      ): {
        value: number | null;
        weight: number;
      } {
        const { values } = params;
        let sum = 0;
        let weight = 0;

        for (let i = 0; i < values.length; i++) {
          const currentValue = values[i];

          if (!currentValue || !currentValue.value) {
            continue;
          }
          sum += currentValue.value * currentValue.weight;
          weight += currentValue.weight;
        }

        const value = weight > 0 ? sum / weight : 0;

        // If the weight or value hasn't changed, return the existing object to avoid triggering change detection.
        const existingAggData =
          params.rowNode?.aggData?.[params.column?.getColId()];
        if (
          existingAggData &&
          existingAggData.weight === weight &&
          existingAggData.value === value
        ) {
          return existingAggData;
        }

        return { value, weight };
      },
    };
  }, []);

  return (
    <div className="flex flex-col gap-2">
      <div className="rounded-md bg-yellow-100 p-2 text-sm text-yellow-800 lg:hidden">
        The holdings table is best viewed on a larger screen.
      </div>
      <Divider />
      {/* Reporting period selector */}
      <div className="flex flex-row items-center gap-2">
        Reporting period:
        <Select
          value={reportingPeriod}
          onValueChange={(value) => {
            const period = Object.values(ReportingPeriod).find(
              (p) => p === value
            );
            if (period) setReportingPeriod(period);
          }}
        >
          <SelectTrigger className="w-fit max-w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.values(ReportingPeriod).map((period) => (
              <SelectItem key={period} value={period}>
                {reportingPeriodOptions[period] ?? period}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {holdingsDataFetcher.state !== "idle" && (
          <div className="flex items-center gap-2">
            <Spinner />
            Updating…
          </div>
        )}
      </div>

      {/* Query input and views sheet */}
      <div className="ag-theme-quartz flex h-[800px] flex-col">
        {/* Query input */}
        <div className="flex flex-row items-center justify-between">
          <holdingsDataViewFetcher.Form
            method="post"
            className="flex w-full items-center gap-2 p-2"
          >
            <Input
              id="holdings-data-view-query-input"
              type="text"
              name="query"
              placeholder="e.g. Show holdings by account name"
            />
            <input
              type="hidden"
              name="actionType"
              value="get-holdings-data-view"
            />
            <input type="hidden" name="id" value={clientID} />
            <input
              type="hidden"
              name="accountNames"
              value={accountsForRowData(rowData)}
            />
            <input
              type="hidden"
              name="strategies"
              value={strategiesFromRowData(rowData)}
            />
            <input
              type="hidden"
              name="taxStatuses"
              value={taxStatusesFromRowData(rowData)}
            />
            <input
              type="hidden"
              name="timeHorizons"
              value={timeHorizonsFromRowData(rowData)}
            />
            <input
              type="hidden"
              name="tickerSymbols"
              value={tickerSymbolsFromRowData(rowData)}
            />
            <input
              type="hidden"
              name="assetClasses"
              value={assetClassesFromRowData(rowData)}
            />
            {holdingsDataViewFetcher.state === "idle" ? (
              <Button type="submit">
                <MagicWandIcon />
                Update Table
              </Button>
            ) : (
              <Button disabled type="submit">
                <Spinner />
                Updating…
              </Button>
            )}
          </holdingsDataViewFetcher.Form>
          {/* View editor*/}
          <div className="flex items-center gap-2">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline">Views</Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Manage Table Views</SheetTitle>
                  <SheetDescription>
                    Save, load, and manage your custom table views.
                  </SheetDescription>
                </SheetHeader>
                <div className="flex flex-col gap-4 py-4">
                  <div className="flex flex-col gap-2">
                    <h3 className="font-semibold">Save New View</h3>
                    <div className="flex items-center gap-2">
                      <Input
                        value={newGridStateName}
                        onChange={(e) => setNewGridStateName(e.target.value)}
                        placeholder="e.g. My Custom View"
                      />
                      <Button onClick={saveGridState}>Save</Button>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <h3 className="font-semibold">Existing Views</h3>
                    {Object.keys(savedGridStates).length === 0 && (
                      <p className="text-sm text-gray-500">No saved views.</p>
                    )}
                    <div className="flex flex-col gap-2">
                      {Object.keys(savedGridStates).map((name) => (
                        <div
                          key={name}
                          className="flex items-center justify-between rounded-md border p-2"
                        >
                          <span className="flex-grow">{name}</span>
                          <div className="flex gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => restoreGridState(name)}
                            >
                              Apply
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => overwriteGridState(name)}
                            >
                              Overwrite
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => deleteGridState(name)}
                            >
                              Delete
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="border-t pt-4">
                    <Button
                      variant="destructive"
                      className="w-full"
                      onClick={() => {
                        setColDefs(columnDefs);
                        setPivotMode(false);
                        if (initialGridState) {
                          gridAPI?.setState(initialGridState);
                        }
                      }}
                    >
                      Reset to default
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* Grid: All holdings */}
        <AgGridReact<HoldingData>
          onGridReady={(params) => {
            setGridAPI(params.api);
            setInitialGridState(params.api.getState());
          }}
          theme={themeQuartz}
          rowData={rowData}
          columnDefs={colDefs}
          pivotMode={pivotMode}
          rowGroupPanelShow="onlyWhenGrouping"
          sideBar={"columns"}
          aggFuncs={aggFuncs}
          suppressAggFuncInHeader={true}
          enableCharts={true}
          cellSelection={true}
        />
      </div>

      <Divider className="my-8" />

      {/* Two additional tables below for percentage change and amount */}
      <div className="flex h-[500px] w-full gap-8">
        {/* Holdings by amount */}
        <div className="flex flex-1 flex-col gap-2">
          <h3>Holdings by amount</h3>
          <HoldingsByColumnChart
            data={rowData}
            columnDef={currentValueBaseColumnDef}
            comparator={(a, b) => b.currentValue - a.currentValue}
          />
        </div>
        {/* Holdings by percentage change */}
        <div className="flex flex-1 flex-col gap-2">
          <h3>Holdings by percentage change</h3>
          <HoldingsByColumnChart
            data={rowData}
            columnDef={percentageChangeBaseColumnDef}
            comparator={(a, b) => b.percentageChange - a.percentageChange}
          />
        </div>
      </div>
    </div>
  );
};
