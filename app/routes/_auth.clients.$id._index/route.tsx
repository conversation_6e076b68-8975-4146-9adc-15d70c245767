import {
  type LoaderFunctionArgs,
  type ActionFunction,
  useLoaderData,
  useSubmit,
  useRevalidator,
  useParams,
  MetaFunction,
  useFetcher,
} from "react-router";
import { BackButton } from "~/@ui/buttons/BackButton";
import {
  BanknoteArrowDown,
  BanknoteArrowUp,
  Building2,
  ClipboardList,
  Clock,
  PiggyBank,
} from "lucide-react";
import "react-toastify/dist/ReactToastify.min.css";
import { toast, Id } from "react-toastify";
import { HeaderV2, SidebarV2, PageTitleText } from "~/@ui/layout/LayoutV2";
import { Button } from "~/@shadcn/ui/button";
import { Separator } from "~/@shadcn/ui/separator";
import { ClientTabGroup } from "~/routes/_auth.clients.$id._index/ClientsTabGroup";
import { NotesTab } from "./NotesTab";
import { ClientRecapTab } from "./ClientRecapTab";
import { HoldingsTab } from "./HoldingsTab";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { useEffect, useRef, useState } from "react";
import { Typography } from "~/@ui/Typography";
import { formatCurrency } from "~/utils";
import { format } from "date-fns";
import { AfterHydration } from "~/utils/hydration";
import AskMeAnythingForClient from "~/@ui/AskMeAnythingForClient";
import {
  ClientApi,
  ClientRecapStatus,
  Configuration,
  HoldingsApi,
  HoldingsResponse,
  HoldingsDataViewResponse,
  SearchApi,
} from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import { z } from "zod";
import { useFlag } from "~/context/flags";

const ClientBasicInfoStruct = z.object({
  gross_annual_income: z.number(),
  assets: z.number(),
  liabilities: z.number(),
  non_liquid_assets: z.number(),
});

const usePollingOnPrepStatus = (status: ClientRecapStatus | null) => {
  const revalidator = useRevalidator();

  useEffect(() => {
    if (status !== ClientRecapStatus.Processing) return;

    const intervalID = setInterval(() => {
      if (navigator.onLine) {
        revalidator.revalidate();
      }
    }, 10_000);

    return () => clearInterval(intervalID);
  }, [status, revalidator]);
};

// Constants
const ERROR_MISSING_PARAMETER = 'Missing route parameter "id"';

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  if (!params.id) throw new Error(ERROR_MISSING_PARAMETER);

  const configuration = new Configuration(
    await configurationParameters(request)
  );
  const client = await new ClientApi(configuration).clientGetClient({
    clientId: params.id,
  });
  return { client };
};

export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `Client - ${data?.client.name ?? "view details"}` },
    { name: "description", content: "View and edit client" },
  ];
};

export const action: ActionFunction = async ({ request, params }) => {
  try {
    if (!params.id) throw new Error(ERROR_MISSING_PARAMETER);
    const clientId = params.id;
    const contentType = request.headers.get("content-type");

    let data;
    if (contentType && contentType.includes("application/json")) {
      data = await request.json();
    } else if (
      contentType &&
      (contentType.includes("multipart/form-data") ||
        contentType.includes("application/x-www-form-urlencoded"))
    ) {
      const formData = await request.formData();
      data = Object.fromEntries(formData.entries());
    } else {
      throw new Error("Unsupported content type");
    }
    const configuration = new Configuration(
      await configurationParameters(request)
    );

    switch (data.actionType) {
      case "search-client": {
        const { answer } = await new SearchApi(
          configuration
        ).searchSearchClient({
          clientId,
          query: data.query,
        });
        return {
          actionType: "search-client",
          success: true,
          answer: answer,
        };
      }
      case "generate-meeting-prep": {
        const response = await new ClientApi(
          configuration
        ).clientGenerateClientRecap({ clientId });
        return { success: response.status !== ClientRecapStatus.Failed };
      }
      case "get-holdings-data": {
        return await new HoldingsApi(configuration).holdingsHoldings({
          clientUuid: clientId,
          reportingPeriod: data.reportingPeriod,
        });
      }
      case "get-holdings-data-view": {
        return await new HoldingsApi(configuration).holdingsHoldingsDataView({
          query: data.query,
          bodyHoldingsHoldingsDataView: {
            accountNames: data.accountNames ? data.accountNames.split(",") : [],
            strategies: data.strategies ? data.strategies.split(",") : [],
            taxStatuses: data.taxStatuses ? data.taxStatuses.split(",") : [],
            timeHorizons: data.timeHorizons ? data.timeHorizons.split(",") : [],
            tickerSymbols: data.tickerSymbols
              ? data.tickerSymbols.split(",")
              : [],
            assetClasses: data.assetClasses ? data.assetClasses.split(",") : [],
          },
        });
      }
      default:
        throw new Error(`Unsupported action type: ${data.actionType}`);
    }
  } catch (error: any) {
    return { success: false, error: error.message };
  }
};

const Route = () => {
  const { id } = useParams();
  const { client } = useLoaderData<typeof loader>();
  const submit = useSubmit();
  const holdingsDataFetcher = useFetcher<HoldingsResponse>();
  const holdingsDataViewFetcher = useFetcher<HoldingsDataViewResponse>();
  const toastId = useRef<Id | null>(null);
  const [query, setQuery] = useState("");
  const [triggerSearch, setTriggerSearch] = useState(false);
  const enableHoldingsTab = useFlag("EnableClientHoldingsTab");

  const handleSyncClick = () => {
    const formData = new FormData();
    formData.append("actionType", "generate-meeting-prep");
    submit(formData, {
      method: "post",
      action: `/clients/${id}`,
      encType: "multipart/form-data",
    });

    if (toastId.current === null || !toast.isActive(toastId.current)) {
      toastId.current = toast.info(
        "Generating client recap. This may take a few moments...",
        {
          position: "top-right",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "colored",
        }
      );
    }
  };

  const result = ClientBasicInfoStruct.safeParse(client.basicInfo);
  const basicInfo = result.success ? result.data : undefined;

  usePollingOnPrepStatus(client.recapStatus);

  return (
    <SidebarV2
      favorSidebarOnMobile
      header={
        <HeaderV2
          className="lg:gap-0"
          left={
            <BackButton
              className="lg:hidden"
              to="/clients"
              tooltip="Back to clients"
            />
          }
          right={
            <div className="flex w-full items-center gap-2">
              <AskMeAnythingForClient
                clientId={id!}
                query={query}
                setQuery={setQuery}
                triggerSearch={triggerSearch}
                setTriggerSearch={setTriggerSearch}
              />
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    onClick={handleSyncClick}
                    className="flex items-center justify-center space-x-2 rounded-lg bg-green-500 px-4 py-2 text-white 
                transition-transform duration-300 hover:bg-green-600 active:bg-green-700
                "
                    disabled={
                      client.recapStatus === ClientRecapStatus.Processing
                    }
                  >
                    <ClipboardList className="!h-5 !w-5" />
                    Generate Client Recap
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Generate Client Recap</TooltipContent>
              </Tooltip>
            </div>
          }
        />
      }
    >
      <div className="flex flex-col gap-3 self-stretch px-6 pb-6">
        <div className="flex items-center justify-between">
          <PageTitleText className="text-2xl font-semibold text-gray-800">
            {client.name}
          </PageTitleText>
          {(
            [
              ClientRecapStatus.Processing,
              ClientRecapStatus.Failed,
            ] as (ClientRecapStatus | null)[]
          ).includes(client.recapStatus) && (
            <div
              className={`flex items-center rounded-md px-3 py-1 opacity-80 shadow-md ${
                client.recapStatus === ClientRecapStatus.Processing
                  ? "bg-primary"
                  : "bg-red-600"
              }`}
            >
              <Typography className="whitespace-nowrap text-sm text-white">
                {client.recapStatus === ClientRecapStatus.Processing
                  ? "Client recap generation in progress"
                  : "Client recap generation failed. Please try again."}
              </Typography>
            </div>
          )}
        </div>
        <div className="grid grid-cols-2 gap-2">
          {basicInfo && (
            <>
              {basicInfo.gross_annual_income && (
                <Typography className="flex items-center text-gray-600">
                  <BanknoteArrowUp size={20} className="mr-2 text-green-500" />
                  <span className="text-sm font-medium">
                    Gross Annual Income:
                  </span>
                  <span className="ml-auto">
                    {formatCurrency(basicInfo.gross_annual_income)}
                  </span>
                </Typography>
              )}

              {basicInfo.assets && (
                <Typography className="flex items-center text-gray-600">
                  <PiggyBank size={20} className="mr-2 text-blue-500" />
                  <span className="text-sm font-medium">Assets:</span>
                  <span className="ml-auto">
                    {formatCurrency(basicInfo.assets)}
                  </span>
                </Typography>
              )}

              {basicInfo.liabilities && (
                <Typography className="flex items-center text-gray-600">
                  <BanknoteArrowDown
                    size={20}
                    className="mr-2 text-yellow-500"
                  />
                  <span className="text-sm font-medium">Liabilities:</span>
                  <span className="ml-auto">
                    {formatCurrency(basicInfo.liabilities)}
                  </span>
                </Typography>
              )}

              {basicInfo.non_liquid_assets && (
                <Typography className="flex items-center text-gray-600">
                  <Building2 size={20} className="mr-2 text-indigo-500" />
                  <span className="text-sm font-medium">
                    Non-Liquid Assets:
                  </span>
                  <span className="ml-auto">
                    {formatCurrency(basicInfo.non_liquid_assets)}
                  </span>
                </Typography>
              )}
            </>
          )}

          {client.recap && (
            <Typography className="flex items-center text-gray-600">
              <Clock className="mr-2 h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium">Created At:</span>
              <AfterHydration>
                <span className="ml-auto">
                  {format(
                    new Date(client.recap.createdAt),
                    "ccc, MMM do, h:mm aaa"
                  )}
                </span>
              </AfterHydration>
            </Typography>
          )}
        </div>

        <Separator className="my-2" />

        <ClientTabGroup
          notesTab={<NotesTab notes={client.notes} />}
          clientRecapTab={<ClientRecapTab recap={client.recap} />}
          holdingsTab={
            enableHoldingsTab ? (
              <HoldingsTab
                clientID={id!}
                holdingsDataFetcher={holdingsDataFetcher}
                holdingsDataViewFetcher={holdingsDataViewFetcher}
              />
            ) : undefined
          }
        />
      </div>
    </SidebarV2>
  );
};

export default Route;
