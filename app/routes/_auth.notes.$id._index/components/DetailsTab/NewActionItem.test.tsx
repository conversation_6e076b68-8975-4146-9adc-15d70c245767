import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { toast } from "react-toastify";
import { useRevalidator } from "react-router";

import NewActionItem from "./NewActionItem";
import { fetchPost } from "~/utils/fetch";
import { UserAgentProvider } from "~/context/userAgent";

// Mock external dependencies
vi.mock("react-toastify", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock("~/utils/fetch", () => ({
  fetchPost: vi.fn(),
}));

const mockRevalidate = vi.fn();

vi.mock("react-router", async (importOriginal) => {
  const actual = await importOriginal<typeof import("react-router")>();
  return {
    ...actual,
    useRevalidator: () => ({ revalidate: mockRevalidate }),
  };
});

describe("NewActionItem", () => {
  const mockOnDone = vi.fn();
  const defaultProps = {
    parentNoteUuid: "test-note-uuid",
    onDone: mockOnDone,
  };

  const renderWithUserAgent = (
    component: React.ReactElement,
    isMobile = false
  ) => {
    const userAgentValue = {
      isMobile,
      isAndroid: false,
      isIos: false,
      isFirefox: false,
      isChrome: true,
      isSafari: false,
    };

    return render(
      <UserAgentProvider value={userAgentValue}>{component}</UserAgentProvider>
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("renders the component with correct placeholder text for desktop", () => {
    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    expect(
      screen.getByPlaceholderText(
        "Type a new action item… Press ENTER (⏎) to save"
      )
    ).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Save" })).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Save & Add another" })
    ).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Cancel" })).toBeInTheDocument();
  });

  it("renders the component with correct placeholder text for mobile", () => {
    renderWithUserAgent(<NewActionItem {...defaultProps} />, true);

    expect(
      screen.getByPlaceholderText("Type a new action item")
    ).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: "Save & Add" })
    ).toBeInTheDocument();
  });

  it("disables save buttons when title is empty", () => {
    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const saveButton = screen.getByRole("button", { name: "Save" });
    const saveAndAddButton = screen.getByRole("button", {
      name: "Save & Add another",
    });

    expect(saveButton).toBeDisabled();
    expect(saveAndAddButton).toBeDisabled();
  });

  it("enables save buttons when title is entered", async () => {
    const user = userEvent.setup();
    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");

    const saveButton = screen.getByRole("button", { name: "Save" });
    const saveAndAddButton = screen.getByRole("button", {
      name: "Save & Add another",
    });

    expect(saveButton).not.toBeDisabled();
    expect(saveAndAddButton).not.toBeDisabled();
  });

  it("calls onSave when Save button is clicked", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");

    const saveButton = screen.getByRole("button", { name: "Save" });
    await user.click(saveButton);

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/tasks/create", {
        parentNoteUuid: "test-note-uuid",
        title: "New action item",
      });
    });

    expect(vi.mocked(toast.success)).toHaveBeenCalledWith(
      "New action item added"
    );
    expect(mockRevalidate).toHaveBeenCalled();
    expect(mockOnDone).toHaveBeenCalled();
  });

  it("calls onSave with shouldAddAnother=true when Save & Add button is clicked", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");

    const saveAndAddButton = screen.getByRole("button", {
      name: "Save & Add another",
    });
    await user.click(saveAndAddButton);

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/tasks/create", {
        parentNoteUuid: "test-note-uuid",
        title: "New action item",
      });
    });

    expect(vi.mocked(toast.success)).toHaveBeenCalledWith(
      "New action item added"
    );
    expect(mockRevalidate).toHaveBeenCalled();
    expect(mockOnDone).not.toHaveBeenCalled();

    // Check that the textarea is cleared for adding another item
    expect(textarea).toHaveValue("");
  });

  it("saves on Enter key press without modifier keys", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");
    await user.keyboard("{Enter}");

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/tasks/create", {
        parentNoteUuid: "test-note-uuid",
        title: "New action item",
      });
    });

    expect(mockOnDone).toHaveBeenCalled();
  });

  it("saves and adds another on Cmd+Enter key press", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");
    await user.keyboard("{Meta>}{Enter}{/Meta}");

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/tasks/create", {
        parentNoteUuid: "test-note-uuid",
        title: "New action item",
      });
    });

    expect(mockOnDone).not.toHaveBeenCalled();
    expect(textarea).toHaveValue("");
    expect(textarea).toHaveFocus();
  });

  it("saves and adds another on Ctrl+Enter key press", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: true });

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");
    await user.keyboard("{Control>}{Enter}{/Control}");

    await waitFor(() => {
      expect(fetchPost).toHaveBeenCalledWith("/feapi/tasks/create", {
        parentNoteUuid: "test-note-uuid",
        title: "New action item",
      });
    });

    expect(mockOnDone).not.toHaveBeenCalled();
    expect(textarea).toHaveValue("");
  });

  it("does not save when title is empty or whitespace only", async () => {
    const user = userEvent.setup();
    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );

    // Test with empty string
    await user.keyboard("{Enter}");
    expect(fetchPost).not.toHaveBeenCalled();

    // Test with whitespace only
    await user.type(textarea, "   ");
    await user.keyboard("{Enter}");
    expect(fetchPost).not.toHaveBeenCalled();
  });

  it("shows loading state during save", async () => {
    const user = userEvent.setup();

    // Mock a delayed response
    vi.mocked(fetchPost).mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve({ success: true }), 100);
        })
    );

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");

    const saveButton = screen.getByRole("button", { name: "Save" });
    await user.click(saveButton);

    // Check loading state
    expect(screen.getByText("Saving…")).toBeInTheDocument();
    expect(
      screen.queryByRole("button", { name: "Save & Add another" })
    ).not.toBeInTheDocument();
    expect(
      screen.queryByRole("button", { name: "Cancel" })
    ).not.toBeInTheDocument();

    // Wait for save to complete
    await waitFor(() => {
      expect(screen.queryByText("Saving…")).not.toBeInTheDocument();
    });
  });

  it("handles save error", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockResolvedValue({ success: false });

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");

    const saveButton = screen.getByRole("button", { name: "Save" });
    await user.click(saveButton);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Failed to create task. Please try again."
      );
    });

    expect(mockRevalidate).not.toHaveBeenCalled();
    expect(mockOnDone).not.toHaveBeenCalled();
  });

  it("handles network error", async () => {
    const user = userEvent.setup();
    vi.mocked(fetchPost).mockRejectedValue(new Error("Network error"));

    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    await user.type(textarea, "New action item");

    const saveButton = screen.getByRole("button", { name: "Save" });
    await user.click(saveButton);

    await waitFor(() => {
      expect(vi.mocked(toast.error)).toHaveBeenCalledWith(
        "Failed to create task. Please try again."
      );
    });
  });

  it("calls onDone when Cancel button is clicked", async () => {
    const user = userEvent.setup();
    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const cancelButton = screen.getByRole("button", { name: "Cancel" });
    await user.click(cancelButton);

    expect(mockOnDone).toHaveBeenCalled();
  });

  it("focuses the textarea on mount", () => {
    renderWithUserAgent(<NewActionItem {...defaultProps} />);

    const textarea = screen.getByPlaceholderText(
      "Type a new action item… Press ENTER (⏎) to save"
    );
    expect(textarea).toHaveFocus();
  });
});
