import { Check, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, X } from "lucide-react";
import { useState } from "react";
import { useRevalidator } from "react-router";
import { toast } from "react-toastify";
import { Button } from "~/@shadcn/ui/button";

import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { useUserAgent } from "~/context/userAgent";
import { fetchPost } from "~/utils/fetch";

type Props = {
  parentNoteUuid: string;
  onDone: () => void;
};

const NewActionItem = (props: Props) => {
  const { parentNoteUuid, onDone } = props;

  const [title, setTitle] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const revalidator = useRevalidator();
  const { isMobile } = useUserAgent();

  // save on "ENTER" press
  const onKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();

      // check if Cmd / Ctrl is pressed
      onSave(event.metaKey || event.ctrlKey);
    }
  };

  const onSave = async (shouldAddAnother: boolean) => {
    // check for empty title
    if (!title.trim()) {
      return;
    }

    setIsSaving(true);

    try {
      const { success } = await fetchPost("/feapi/tasks/create", {
        parentNoteUuid,
        title,
      });

      if (success) {
        toast.success("New action item added");
        revalidator.revalidate();

        if (shouldAddAnother) {
          // reset all input fields
          setTitle("");
        } else {
          // ask parent to close this
          onDone();
        }
      } else {
        throw new Error();
      }
    } catch (e) {
      toast.error("Failed to create task. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="flex items-start gap-2 rounded-md border border-slate-200 bg-muted p-2">
      <Circle size={20} className="m-0.5 shrink-0 opacity-25" />

      <div className="grow">
        <TextareaGrowable
          className="flex-grow border-none p-2 text-sm !opacity-100 focus:outline-none"
          placeholder={
            isMobile
              ? "Type a new action item"
              : "Type a new action item… Press ENTER (⏎) to save"
          }
          autoFocus
          value={title}
          onChange={(event) => {
            setTitle(event.currentTarget.value);
          }}
          onKeyDown={onKeyDown}
        />

        <div className="mt-2 flex flex-wrap gap-y-1">
          {/* save button */}
          <Button
            variant="outline"
            onClick={() => onSave(false)}
            disabled={isSaving || !title.trim()}
            size="sm"
            className="mr-1"
          >
            <Check size={14} className="mr-1" />
            {isSaving ? "Saving…" : "Save"}
          </Button>

          {/* save & add button */}
          {!isSaving && (
            <Button
              variant="ghost"
              onClick={() => onSave(true)}
              disabled={!title.trim()}
              size="sm"
            >
              <CheckCheck size={14} className="mr-1" />
              {isMobile ? "Save & Add" : "Save & Add another"}
            </Button>
          )}

          {/* cancel button */}
          {!isSaving && (
            <Button variant="ghost" onClick={() => onDone()} size="sm">
              <X size={14} className="mr-1" />
              Cancel
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewActionItem;
