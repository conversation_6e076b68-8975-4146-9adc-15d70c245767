import { useEffect, useState } from "react";
import { RotateCcw, Save } from "lucide-react";

import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "~/@shadcn/ui/sheet";
import { capitalize } from "~/utils/strings";
import { fetchPost } from "~/utils/fetch";

import "react-phone-number-input/style.css";
import renderFormFields from "../utils/renderFormFields";

type Props = {
  onOpenChange: (isOpen: boolean) => void;
  onCreate?: (clientUuid: string, formData: Record<string, any>) => void;
  onError?: (msg: string) => void;
  successMsg?: string;
};

const CreateUserFlow = (props: Props) => {
  const { onOpenChange, onCreate, onError, successMsg } = props;

  const [formData, setFormData] =
    useState<Record<string, string | boolean>>(initializeData());
  const [isInvalid, setIsInvalid] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSaved, setIsSaved] = useState(false);

  // if any required fields are empty; disable CTA
  useEffect(() => {
    const hasEmptyRequiredFields = getFormFields().some(
      (field) => field.required && !formData[field.id]
    );
    setIsInvalid(hasEmptyRequiredFields);
  }, [formData]);

  const onSave = async () => {
    // eslint-disable-next-line no-console
    console.log("formData", formData);

    return;

    // setIsSaving(true);

    // try {
    //   const { success, clientUuid, errorMsg } = await fetchPost(
    //     "/feapi/clients/create",
    //     {
    //       ...formData,
    //     }
    //   );

    //   if (success) {
    //     setIsSaved(true);
    //     onCreate?.(clientUuid, formData);
    //   } else {
    //     onError?.(errorMsg);
    //   }
    // } catch (e) {
    //   // eslint-disable-next-line no-console
    //   console.error("Client creation failed", e);

    //   if (onError) {
    //     if (typeof e === "object" && e !== null && "message" in e) {
    //       onError(e.message as string);
    //     } else {
    //       onError("An error occurred. Failed to create client");
    //     }
    //   }
    // } finally {
    //   setIsSaving(false);
    // }
  };

  // reset form data to initial values (computed from prefilled details)
  const onReset = () => {
    setFormData(initializeData());
  };

  return (
    <Sheet open onOpenChange={onOpenChange}>
      <SheetContent className="flex w-full flex-col">
        <SheetHeader>
          <SheetTitle>Create a New User</SheetTitle>
          <SheetDescription />
        </SheetHeader>

        <div className="flex grow flex-col justify-start gap-4 overflow-auto px-0.5">
          {renderFormFields(getFormFields(), formData, setFormData)}
        </div>

        <SheetFooter className="shrink-0 flex-col gap-y-2 sm:justify-start">
          {isSaved && successMsg && (
            <div className="text-sm text-success">{successMsg}</div>
          )}

          {!(isSaved && successMsg) && (
            <>
              <Button onClick={onSave} disabled={isInvalid || isSaving}>
                <Save />
                {isSaving ? "Creating…" : "Create User"}
              </Button>
              <Button onClick={onReset} variant="ghost" disabled={isSaving}>
                <RotateCcw />
                Reset
              </Button>
            </>
          )}
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};

function initializeData() {
  const data = {
    ...getFormFields().reduce(
      (acc, { id, defaultValue, type }) => {
        acc[id] = defaultValue || (type === "boolean" ? false : "");
        return acc;
      },
      {} as Record<string, string | boolean>
    ),
  };

  return data;
}

function getFormFields() {
  return [
    {
      id: "name",
      label: "Preferred Name",
      type: "text",
      defaultValue: "Jane Doe",
    },
    {
      id: "firstName",
      label: "First Name",
      type: "text",
    },
    {
      id: "middleName",
      label: "Middle Name",
      type: "text",
    },
    {
      id: "lastName",
      label: "Last Name",
      type: "text",
    },
    {
      id: "email",
      label: "Email",
      type: "text",
      required: true,
    },
    {
      id: "phoneNumber",
      label: "Phone",
      type: "phone",
    },
    {
      id: "phonePin",
      label: "Phone PIN",
      type: "text",
    },
    {
      id: "entitlements",
      label: "Entitlements",
      type: "dropdown",
      // TEMP: Mention all (like EntitlementType.MeetingAssistant) once webapp#1312 gets merged
      options: ["meeting_assistant", "client_intelligence"].map(
        (entitlement) => ({
          label: entitlement.split("_").map(capitalize).join(" "),
          value: entitlement,
        })
      ),
    },
    {
      id: "sendWelcomeEmail",
      label: "Send Welcome Email",
      type: "boolean",
    },
  ];
}

export default CreateUserFlow;
