import { useState } from "react";
import { <PERSON> } from "react-router";
import { ArrowLeft } from "lucide-react";

import { But<PERSON> } from "~/@shadcn/ui/button";
import CreateUserFlow from "./components/CreateUserFlow";
import { toast } from "react-toastify";

const Route = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const onCreateUser = () => {
    // show toast
    toast.success("User created");

    // close modal
    setIsCreateModalOpen(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <Button className="mr-2 md:hidden" size="icon-sm" variant="outline">
          <Link to="/settings">
            <ArrowLeft />
          </Link>
        </Button>
        <h2 className="text-2xl font-semibold">Users & Teams</h2>
      </div>

      <Button onClick={() => setIsCreateModalOpen(true)}>Create User</Button>

      {isCreateModalOpen && (
        <CreateUserFlow
          onOpenChange={setIsCreateModalOpen}
          onCreate={onCreateUser}
        />
      )}
    </div>
  );
};

export default Route;
