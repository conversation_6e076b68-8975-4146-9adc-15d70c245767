// @vitest-environment node

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { loader } from "./route";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, NoteApi } from "~/api/openapi/generated";
import { v4 as uuidv4 } from "uuid";

// Mock dependencies
vi.mock("~/api/openapi/configParams");
vi.mock("~/api/openapi/generated");

describe("loader", () => {
  const mockConfigParams = {
    basePath: "http://localhost:8000",
    middleware: [],
  };

  const mocknoteExportNoteRawNote = vi.fn();
  const mockRawPdfData = new ArrayBuffer(1024); // Mock PDF binary data

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock configurationParameters
    vi.mocked(configurationParameters).mockResolvedValue(mockConfigParams);

    // Mock NoteApi
    mocknoteExportNoteRawNote.mockResolvedValue({ raw: mockRawPdfData });
    vi.mocked(NoteApi).mockImplementation(
      () =>
        ({
          noteExportNoteRawNote: mocknoteExportNoteRawNote,
        }) as any
    );

    // Mock Configuration
    vi.mocked(Configuration).mockImplementation((params) => params as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should export a note successfully with valid noteId", async () => {
    const noteId = uuidv4();
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: noteId };

    const result = await loader({ request, params, context: {} });

    expect(result).toBe(mockRawPdfData);

    expect(configurationParameters).toHaveBeenCalledWith(request);
    expect(Configuration).toHaveBeenCalledWith(mockConfigParams);
    expect(NoteApi).toHaveBeenCalledWith(mockConfigParams);
    expect(mocknoteExportNoteRawNote).toHaveBeenCalledWith({
      noteId,
      exportNoteToPDFRequest: {},
    });
  });

  it("should throw 400 error when noteId is missing", async () => {
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = {}; // No id parameter

    await expect(loader({ request, params, context: {} })).rejects.toThrow(
      expect.objectContaining({
        status: 400,
      })
    );

    expect(mocknoteExportNoteRawNote).not.toHaveBeenCalled();
  });

  it("should throw 400 error when noteId is undefined", async () => {
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: undefined };

    await expect(loader({ request, params, context: {} })).rejects.toThrow(
      expect.objectContaining({
        status: 400,
      })
    );

    expect(mocknoteExportNoteRawNote).not.toHaveBeenCalled();
  });

  it("should throw 400 error when noteId is null", async () => {
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: null as any }; // Type assertion for test case

    await expect(loader({ request, params, context: {} })).rejects.toThrow(
      expect.objectContaining({
        status: 400,
      })
    );

    expect(mocknoteExportNoteRawNote).not.toHaveBeenCalled();
  });

  it("should throw 400 error when noteId is empty string", async () => {
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: "" };

    await expect(loader({ request, params, context: {} })).rejects.toThrow(
      expect.objectContaining({
        status: 400,
      })
    );

    expect(mocknoteExportNoteRawNote).not.toHaveBeenCalled();
  });

  it("should propagate error when configurationParameters throws", async () => {
    const noteId = uuidv4();
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: noteId };

    const configError = new Error("Configuration failed");
    vi.mocked(configurationParameters).mockRejectedValue(configError);

    await expect(loader({ request, params, context: {} })).rejects.toThrow(
      configError
    );

    expect(mocknoteExportNoteRawNote).not.toHaveBeenCalled();
  });

  it("should propagate error when NoteApi.noteExportNoteRawNote throws", async () => {
    const noteId = uuidv4();
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: noteId };

    const apiError = new Error("API call failed");
    mocknoteExportNoteRawNote.mockRejectedValue(apiError);

    await expect(loader({ request, params, context: {} })).rejects.toThrow(
      apiError
    );

    expect(mocknoteExportNoteRawNote).toHaveBeenCalledWith({
      noteId,
      exportNoteToPDFRequest: {},
    });
  });

  it("should handle different types of raw data responses", async () => {
    const noteId = uuidv4();
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: noteId };

    // Test with different mock data types
    const mockBlobData = new Blob(["PDF content"], { type: "application/pdf" });
    mocknoteExportNoteRawNote.mockResolvedValue({ raw: mockBlobData });

    const result = await loader({ request, params, context: {} });

    expect(result).toBe(mockBlobData);
    expect(mocknoteExportNoteRawNote).toHaveBeenCalledWith({
      noteId,
      exportNoteToPDFRequest: {},
    });
  });

  it("should handle malformed response from API", async () => {
    const noteId = uuidv4();
    const request = new Request("http://localhost:3000/feapi/notes/export");
    const params = { id: noteId };

    // Mock response without raw property
    mocknoteExportNoteRawNote.mockResolvedValue({});

    const result = await loader({ request, params, context: {} });

    expect(result).toBeUndefined();
  });
});
